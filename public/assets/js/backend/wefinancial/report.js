define(['jquery', 'bootstrap', 'backend', 'table', 'form', '/assets/addons/wefinancial/js/common.js'], function ($, undefined, Backend, Table, Form, wef) {

  var Controller = {
    index: function () {
      // 初始化
      Controller.api.init();

      // 绑定事件
      Controller.api.bindEvents();

      // 初始化悬停效果
      Controller.api.initHoverEffects();
    },

    detail: function () {
      // 绑定详情页事件
      Controller.api.bindDetailEvents();
    },
    subjectconfig: function () {
      var itemId = Fast.api.query("item_id")
      Controller.api.bindModalEvents($('#subject-config-form'), itemId)
      Form.api.bindevent($("form[role=form]"));
    },
    test: function () {
      Form.api.bindevent($("form[role=form]"));
    },

    api: {
      init: function () {
      },

      bindEvents: function () {
        // 刷新按钮事件
        $(document).on('click', '.btn-refresh', function () {
          window.location.reload();
        });

        // 报表模板点击事件
        $(document).on('click', '.report-template-card', function () {
          var templateId = $(this).data('template-id');
          var templateName = $(this).find('.template-name').text();

          // 在新标签页中打开报表详情（不带期间参数，由详情页面处理）
          var url = 'wefinancial/report/detail?template_id=' + templateId;
          Backend.api.addtabs(url, templateName, true);
        });
      },

      initHoverEffects: function () {
        // 添加悬停效果
        $('.report-template-card').hover(
          function () {
            $(this).find('.panel').addClass('panel-primary').removeClass('panel-default');
          },
          function () {
            $(this).find('.panel').addClass('panel-default').removeClass('panel-primary');
          }
        );
      },

      bindDetailEvents: function () {
        // 期间选择变化事件
        $(document).on('change', '#period-select', function () {
          var newPeriod = $(this).val();
          $('#report-period-text').text(newPeriod);
          // 重新加载页面以获取新期间的数据
          var templateId = window.templateId;
          if (templateId) {
            window.location.href = 'detail?template_id=' + templateId + '&period=' + newPeriod;
          }
        });

        // 导出按钮事件
        $(document).on('click', '.btn-export', function () {
          Controller.api.exportExcel();
        });

        // 打印按钮事件
        $(document).on('click', '.btn-print', function () {
          Controller.api.printReport();
        });
      },

      printReport: function () {
        // 打印报表
        var printContents = document.getElementById('print-area').innerHTML;
        var originalContents = document.body.innerHTML;

        document.body.innerHTML = printContents;
        window.print();
        document.body.innerHTML = originalContents;

        // 重新绑定事件
        Controller.api.bindDetailEvents();
      },

      exportExcel: function () {
        // 导出Excel
        var templateId = window.templateId;
        var period = $('#period-select').val();

        if (!templateId || !period) {
          Toastr.error('参数错误');
          return;
        }

        // 创建一个隐藏的表单来提交导出请求
        var form = $('<form>', {
          'method': 'POST',
          'action': 'export',
          'target': '_blank'
        });

        form.append($('<input>', {
          'type': 'hidden',
          'name': 'template_id',
          'value': templateId
        }));

        form.append($('<input>', {
          'type': 'hidden',
          'name': 'period',
          'value': period
        }));

        $('body').append(form);
        form.submit();
        form.remove();
      },

      getSubjectRowHtml: function (subject) {
        subject = subject || {};
        var template = $('#subject-row-template').html();
        var $row = $(template);

        // 设置科目信息
        if (subject.subject_id) {
          $row.find('.subject-id').val(subject.subject_id);
          var subjectText = subject.code ? (subject.code + ' ' + subject.subject_name) : '';
          $row.find('.subject-selector').val(subjectText).data('selectpage-id', subject.subject_id);
        }

        // 设置选项值
        if (subject.operation_type) {
          $row.find('.operation-type').val(subject.operation_type);
        }
        if (subject.balance_direction) {
          $row.find('.balance-direction').val(subject.balance_direction);
        }
        if (subject.weight) {
          $row.find('.weight').val(subject.weight);
        }

        return $row[0].outerHTML;
      },

      bindModalEvents: function ($modal, itemId) {
        // 添加科目按钮
        $modal.on('click', '.btn-add-subject', function () {
          var rowHtml = Controller.api.getSubjectRowHtml();
          var $tbody = $modal.find('#subject-config-tbody');

          // 如果是空提示行，先移除
          if ($tbody.find('tr').length === 1 && $tbody.find('.text-muted').length > 0) {
            $tbody.empty();
          }

          var $newRow = $(rowHtml);
          $tbody.append($newRow);

          // 初始化新行的selectpage组件
          Form.api.bindevent($("form[role=form]"));
        });

        // 删除科目按钮
        $modal.on('click', '.btn-remove-subject', function () {
          $(this).closest('tr').remove();

          var $tbody = $modal.find('#subject-config-tbody');
          if ($tbody.find('tr').length === 0) {
            $tbody.append('<tr><td colspan="6" class="text-center text-muted">暂无科目配置</td></tr>');
          }
        });

        // 保存配置按钮
        $modal.on('click', '.btn-save-config', function () {
          Controller.api.saveSubjectConfig($modal, itemId);
        });
      },


      saveSubjectConfig: function ($modal, itemId) {
        // 获取itemId（如果没有传入，从隐藏字段获取）
        if (!itemId) {
          itemId = $modal.find('#current-item-id').val();
        }

        // 收集配置数据
        var subjects = [];
        $modal.find('#subject-config-tbody tr').each(function () {
          var $row = $(this);
          if ($row.find('.text-muted').length > 0) {
            return; // 跳过空提示行
          }

          var $selectpage = $row.find('.subject-selector');
          var subjectId = $selectpage.next().val();

          if (!subjectId) {
            return; // 跳过未选择科目的行
          }

          subjects.push({
            subject_id: subjectId,
            operation_type: $row.find('.operation-type').val(),
            balance_direction: $row.find('.balance-direction').val(),
            weight: parseFloat($row.find('.weight').val()) || 1.0000
          });
        });

        // 保存配置
        $.ajax({
          url: 'wefinancial/report/saveItemSubjects',
          type: 'POST',
          data: {
            item_id: itemId,
            subjects: subjects
          },
          dataType: 'json',
          success: function (ret) {
            if (ret.code === 1) {
              top.Toastr.success('保存成功');
              // 关闭弹窗并刷新页面
              parent.layer.closeAll();
              parent.window.location.reload();
            } else {
              Toastr.error(ret.msg);
            }
          },
          error: function () {
            Toastr.error('保存失败');
          }
        });
      }
    }
  };

  return Controller;
});
