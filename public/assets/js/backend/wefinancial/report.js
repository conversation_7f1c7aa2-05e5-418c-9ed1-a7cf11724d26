define(['jquery', 'bootstrap', 'backend', 'table', 'form', '/assets/addons/wefinancial/js/common.js'], function ($, undefined, Backend, Table, Form, wef) {

  var Controller = {
    index: function () {
      // 初始化
      Controller.api.init();

      // 绑定事件
      Controller.api.bindEvents();

      // 初始化悬停效果
      Controller.api.initHoverEffects();
    },

    detail: function () {
      // 绑定详情页事件
      Controller.api.bindDetailEvents();
    },
    subjectconfig: function () {
      var itemId = Fast.api.query("item_id")
      Controller.api.bindModalEvents($('#subject-config-form'), itemId)
      Form.api.bindevent($("form[role=form]"));
    },
    test: function () {
      Form.api.bindevent($("form[role=form]"));
    },

    api: {
      init: function () {
      },

      bindEvents: function () {
        // 刷新按钮事件
        $(document).on('click', '.btn-refresh', function () {
          window.location.reload();
        });

        // 报表模板点击事件
        $(document).on('click', '.report-template-card', function () {
          var templateId = $(this).data('template-id');
          var templateName = $(this).find('.template-name').text();

          // 在新标签页中打开报表详情（不带期间参数，由详情页面处理）
          var url = 'wefinancial/report/detail?template_id=' + templateId;
          Backend.api.addtabs(url, templateName, true);
        });
      },

      initHoverEffects: function () {
        // 添加悬停效果
        $('.report-template-card').hover(
          function () {
            $(this).find('.panel').addClass('panel-primary').removeClass('panel-default');
          },
          function () {
            $(this).find('.panel').addClass('panel-default').removeClass('panel-primary');
          }
        );
      },

      bindDetailEvents: function () {
        // 期间选择变化事件
        $(document).on('change', '#period-select', function () {
          var newPeriod = $(this).val();
          $('#report-period-text').text(newPeriod);
          // 重新加载页面以获取新期间的数据
          var templateId = window.templateId;
          if (templateId) {
            window.location.href = 'detail?template_id=' + templateId + '&period=' + newPeriod;
          }
        });

        // 导出按钮事件
        $(document).on('click', '.btn-export', function () {
          Controller.api.exportExcel();
        });

        // 打印按钮事件
        $(document).on('click', '.btn-print', function () {
          Controller.api.printReport();
        });
      },

      printReport: function () {
        // 打印报表 - 使用新窗口方式避免样式丢失
        var printContents = document.getElementById('print-area').innerHTML;

        if (!printContents) {
          Toastr.error('打印内容为空');
          return;
        }

        // 创建新的打印窗口
        var printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

        if (!printWindow) {
          Toastr.error('无法打开打印窗口，请检查浏览器弹窗设置');
          return;
        }

        // 构建完整的HTML文档
        var htmlContent = '<!DOCTYPE html>' +
          '<html>' +
          '<head>' +
          '<meta charset="utf-8">' +
          '<title>报表打印</title>' +
          '<link rel="stylesheet" href="/assets/css/bootstrap.css">' +
          '<style>' +
          '@media print {' +
          '.no-print { display: none !important; }' +
          'body { margin: 0; padding: 20px; }' +
          '.table { border-collapse: collapse !important; }' +
          '.table td, .table th { background-color: #fff !important; border: 1px solid #000 !important; padding: 8px !important; }' +
          '.table-bordered th, .table-bordered td { border: 1px solid #000 !important; }' +
          '.report-title { font-size: 18px !important; font-weight: bold !important; margin-bottom: 15px !important; }' +
          '.report-info { margin-bottom: 20px !important; font-size: 14px !important; }' +
          '.btn { display: none !important; }' +
          '.text-center { text-align: center !important; }' +
          '.text-left { text-align: left !important; }' +
          '.text-right { text-align: right !important; }' +
          '}' +
          '@media screen {' +
          'body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; margin: 20px; }' +
          '.table { border-collapse: collapse; width: 100%; }' +
          '.table td, .table th { border: 1px solid #ddd; padding: 8px; }' +
          '.table th { background-color: #f8f9fa; font-weight: bold; }' +
          '.report-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; text-align: center; }' +
          '.report-info { margin-bottom: 20px; font-size: 14px; }' +
          '.btn { display: none; }' +
          '.print-controls { margin: 20px 0; text-align: center; }' +
          '.print-controls button { margin: 0 10px; padding: 8px 16px; }' +
          '}' +
          '</style>' +
          '</head>' +
          '<body>' +
          '<div class="print-controls no-print">' +
          '<button onclick="window.print();" class="btn btn-primary">打印</button>' +
          '<button onclick="window.close();" class="btn btn-default">关闭</button>' +
          '</div>' +
          printContents +
          '<script>' +
          'var printExecuted = false;' +
          'function executePrint() {' +
          'if (!printExecuted) {' +
          'printExecuted = true;' +
          'setTimeout(function() { window.print(); }, 500);' +
          '}' +
          '}' +
          'window.onload = executePrint;' +
          'window.onbeforeprint = function() { console.log("Print dialog opened"); };' +
          'window.onafterprint = function() {' +
          'console.log("Print dialog closed");' +
          'setTimeout(function() {' +
          'if (!window.closed) {' +
          'var shouldClose = confirm("打印完成，是否关闭预览窗口？");' +
          'if (shouldClose) { window.close(); }' +
          '}' +
          '}, 100);' +
          '};' +
          '</script>' +
          '</body>' +
          '</html>';

        try {
          // 写入内容
          printWindow.document.write(htmlContent);
          printWindow.document.close();

          // 聚焦到新窗口
          printWindow.focus();

        } catch (e) {
          console.error('打印窗口创建失败:', e);
          Toastr.error('打印功能异常，请重试');
          if (printWindow && !printWindow.closed) {
            printWindow.close();
          }
        }
      },

      exportExcel: function () {
        // 导出Excel
        var templateId = window.templateId;
        var period = $('#period-select').val();

        if (!templateId || !period) {
          Toastr.error('参数错误');
          return;
        }

        // 创建一个隐藏的表单来提交导出请求
        var form = $('<form>', {
          'method': 'POST',
          'action': 'export',
          'target': '_blank'
        });

        form.append($('<input>', {
          'type': 'hidden',
          'name': 'template_id',
          'value': templateId
        }));

        form.append($('<input>', {
          'type': 'hidden',
          'name': 'period',
          'value': period
        }));

        $('body').append(form);
        form.submit();
        form.remove();
      },

      getSubjectRowHtml: function (subject) {
        subject = subject || {};
        var template = $('#subject-row-template').html();
        var $row = $(template);

        // 设置科目信息
        if (subject.subject_id) {
          $row.find('.subject-id').val(subject.subject_id);
          var subjectText = subject.code ? (subject.code + ' ' + subject.subject_name) : '';
          $row.find('.subject-selector').val(subjectText).data('selectpage-id', subject.subject_id);
        }

        // 设置选项值
        if (subject.operation_type) {
          $row.find('.operation-type').val(subject.operation_type);
        }
        if (subject.balance_direction) {
          $row.find('.balance-direction').val(subject.balance_direction);
        }
        if (subject.weight) {
          $row.find('.weight').val(subject.weight);
        }

        return $row[0].outerHTML;
      },

      bindModalEvents: function ($modal, itemId) {
        // 添加科目按钮
        $modal.on('click', '.btn-add-subject', function () {
          var rowHtml = Controller.api.getSubjectRowHtml();
          var $tbody = $modal.find('#subject-config-tbody');

          // 如果是空提示行，先移除
          if ($tbody.find('tr').length === 1 && $tbody.find('.text-muted').length > 0) {
            $tbody.empty();
          }

          var $newRow = $(rowHtml);
          $tbody.append($newRow);

          // 初始化新行的selectpage组件
          Form.api.bindevent($("form[role=form]"));
        });

        // 删除科目按钮
        $modal.on('click', '.btn-remove-subject', function () {
          $(this).closest('tr').remove();

          var $tbody = $modal.find('#subject-config-tbody');
          if ($tbody.find('tr').length === 0) {
            $tbody.append('<tr><td colspan="6" class="text-center text-muted">暂无科目配置</td></tr>');
          }
        });

        // 保存配置按钮
        $modal.on('click', '.btn-save-config', function () {
          Controller.api.saveSubjectConfig($modal, itemId);
        });
      },


      saveSubjectConfig: function ($modal, itemId) {
        // 获取itemId（如果没有传入，从隐藏字段获取）
        if (!itemId) {
          itemId = $modal.find('#current-item-id').val();
        }

        // 收集配置数据
        var subjects = [];
        $modal.find('#subject-config-tbody tr').each(function () {
          var $row = $(this);
          if ($row.find('.text-muted').length > 0) {
            return; // 跳过空提示行
          }

          var $selectpage = $row.find('.subject-selector');
          var subjectId = $selectpage.next().val();

          if (!subjectId) {
            return; // 跳过未选择科目的行
          }

          subjects.push({
            subject_id: subjectId,
            operation_type: $row.find('.operation-type').val(),
            balance_direction: $row.find('.balance-direction').val(),
            weight: parseFloat($row.find('.weight').val()) || 1.0000
          });
        });

        // 保存配置
        $.ajax({
          url: 'wefinancial/report/saveItemSubjects',
          type: 'POST',
          data: {
            item_id: itemId,
            subjects: subjects
          },
          dataType: 'json',
          success: function (ret) {
            if (ret.code === 1) {
              top.Toastr.success('保存成功');
              // 关闭弹窗并刷新页面
              parent.layer.closeAll();
              parent.window.location.reload();
            } else {
              Toastr.error(ret.msg);
            }
          },
          error: function () {
            Toastr.error('保存失败');
          }
        });
      }
    }
  };

  return Controller;
});
